{"name": "echolounge-desktop", "version": "1.0.0", "description": "EchoLounge Desktop - Mystical AI Chatroom Platform", "main": "src/main.js", "scripts": {"start": "electron .", "dev": "concurrently \"npm run server:dev\" \"npm run electron:dev\"", "electron:dev": "wait-on http://localhost:3000 && electron . --dev", "server:dev": "cd ../server && npm run dev", "build": "npm run build:renderer && npm run build:main", "build:renderer": "cd ../client && npm run build", "build:main": "electron-builder", "dist": "npm run build && electron-builder --publish=never", "pack": "electron-builder --dir"}, "build": {"appId": "com.echolounge.desktop", "productName": "EchoLounge", "directories": {"output": "dist"}, "files": ["src/**/*", "assets/**/*", "../client/build/**/*", "../server/**/*", "!../server/node_modules", "node_modules/**/*"], "extraResources": [{"from": "../server", "to": "server", "filter": ["**/*", "!node_modules"]}], "win": {"target": "nsis", "requestedExecutionLevel": "asInvoker"}, "mac": {"target": "dmg", "category": "public.app-category.social-networking"}, "linux": {"target": "AppImage", "category": "Network"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}, "devDependencies": {"concurrently": "^8.2.1", "electron": "^27.1.3", "electron-builder": "^24.6.4", "wait-on": "^7.2.0"}, "dependencies": {"electron-store": "^8.1.0", "electron-updater": "^6.1.4"}}