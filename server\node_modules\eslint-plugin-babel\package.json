{"name": "eslint-plugin-babel", "version": "5.3.1", "description": "an eslint rule plugin companion to babel-eslint", "main": "index.js", "scripts": {"test": "mocha ./tests/rules/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/babel/eslint-plugin-babel.git"}, "keywords": ["babel", "eslint", "eslintplugin", "eslint-plugin", "babel-es<PERSON>"], "author": "<PERSON> @monasticpanic", "license": "MIT", "engines": {"node": ">=4"}, "bugs": {"url": "https://github.com/babel/eslint-plugin-babel/issues"}, "homepage": "https://github.com/babel/eslint-plugin-babel#readme", "peerDependencies": {"eslint": ">=4.0.0"}, "dependencies": {"eslint-rule-composer": "^0.3.0"}, "devDependencies": {"babel-eslint": "^8.2.2", "eslint": "^4.19.1", "lodash.clonedeep": "^4.5.0", "mocha": "^5.2.0"}}